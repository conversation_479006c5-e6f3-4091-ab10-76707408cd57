<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.UserLoginRecordMapper">
    
    <resultMap type="UserLoginRecord" id="UserLoginRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginTime"    column="login_time"    />
        <result property="ip"    column="ip"    />
        <result property="location"    column="location"    />
        <result property="platform"    column="platform"    />
        <result property="brand"    column="brand"    />
        <result property="model"    column="model"    />
        <result property="version"    column="version"    />
        <result property="sdkVersion"    column="sdk_version"    />
        <result property="isFirstLogin"    column="is_first_login"    />
        <result property="system"    column="system"    />
    </resultMap>

    <sql id="selectUserLoginRecordVo">
        select id, user_id, login_time, ip, location, platform, brand, model, version, sdk_version, is_first_login, system from user_login_record
    </sql>

    <select id="selectUserLoginRecordList" parameterType="UserLoginRecord" resultMap="UserLoginRecordResult">
        <include refid="selectUserLoginRecordVo"/>
        <where>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="brand != null  and brand != ''"> and brand = #{brand}</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="sdkVersion != null  and sdkVersion != ''"> and sdk_version = #{sdkVersion}</if>
            <if test="isFirstLogin != null"> and is_first_login = #{isFirstLogin}</if>
            <if test="system != null  and system != ''"> and system = #{system}</if>
        </where>
    </select>
    
    <select id="selectUserLoginRecordById" parameterType="Long" resultMap="UserLoginRecordResult">
        <include refid="selectUserLoginRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserLoginRecord" parameterType="UserLoginRecord" useGeneratedKeys="true" keyProperty="id">
        insert into user_login_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="loginTime != null">login_time,</if>
            <if test="ip != null and ip != ''">ip,</if>
            <if test="location != null">location,</if>
            <if test="platform != null">platform,</if>
            <if test="brand != null">brand,</if>
            <if test="model != null">model,</if>
            <if test="version != null">version,</if>
            <if test="sdkVersion != null">sdk_version,</if>
            <if test="isFirstLogin != null">is_first_login,</if>
            <if test="system != null">system,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="loginTime != null">#{loginTime},</if>
            <if test="ip != null and ip != ''">#{ip},</if>
            <if test="location != null">#{location},</if>
            <if test="platform != null">#{platform},</if>
            <if test="brand != null">#{brand},</if>
            <if test="model != null">#{model},</if>
            <if test="version != null">#{version},</if>
            <if test="sdkVersion != null">#{sdkVersion},</if>
            <if test="isFirstLogin != null">#{isFirstLogin},</if>
            <if test="system != null">#{system},</if>
         </trim>
    </insert>

    <update id="updateUserLoginRecord" parameterType="UserLoginRecord">
        update user_login_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="loginTime != null">login_time = #{loginTime},</if>
            <if test="ip != null and ip != ''">ip = #{ip},</if>
            <if test="location != null">location = #{location},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="model != null">model = #{model},</if>
            <if test="version != null">version = #{version},</if>
            <if test="sdkVersion != null">sdk_version = #{sdkVersion},</if>
            <if test="isFirstLogin != null">is_first_login = #{isFirstLogin},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserLoginRecordById" parameterType="Long">
        delete from user_login_record where id = #{id}
    </delete>

    <delete id="deleteUserLoginRecordByIds" parameterType="String">
        delete from user_login_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>